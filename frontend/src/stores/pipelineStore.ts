import { create } from 'zustand';

// Define a simple Pipeline interface for the store
interface Pipeline {
  id: string;
  name: string;
  description?: string;
  status?: string;
  config?: any;
}

interface PipelineState {
  pipelines: Pipeline[];
  selectedPipeline: Pipeline | null;
  setPipeline: (pipeline: Pipeline) => void;
  updatePipeline: (pipeline: Pipeline) => void;
  deletePipeline: (pipelineId: string) => void;
  addPipeline: (pipeline: Pipeline) => void;
}

export const usePipelineStore = create<PipelineState>()((set) => ({
  pipelines: [],
  selectedPipeline: null,

  setPipeline: (pipeline) =>
    set((state) => ({
      selectedPipeline: pipeline,
      pipelines: state.pipelines.map((p) =>
        p.id === pipeline.id ? pipeline : p
      ),
    })),

  updatePipeline: (pipeline) =>
    set((state) => ({
      pipelines: state.pipelines.map((p) =>
        p.id === pipeline.id ? pipeline : p
      ),
      selectedPipeline:
        state.selectedPipeline?.id === pipeline.id
          ? pipeline
          : state.selectedPipeline,
    })),

  deletePipeline: (pipelineId) =>
    set((state) => ({
      pipelines: state.pipelines.filter((p) => p.id !== pipelineId),
      selectedPipeline:
        state.selectedPipeline?.id === pipelineId
          ? null
          : state.selectedPipeline,
    })),

  addPipeline: (pipeline) =>
    set((state) => ({
      pipelines: [...state.pipelines, pipeline],
    })),
}));