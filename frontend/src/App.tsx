import React from 'react';
import { BrowserRouter as Router, Routes, Route, useParams, useNavigate } from 'react-router-dom';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { theme } from './theme';
import Layout from './components/Layout';
import { PipelineList } from './components/PipelineList';
import { PipelineEditor } from './components/PipelineEditor';
import PipelineTemplateManager from './components/PipelineTemplateManager';
import PipelineTemplateInstantiator from './components/PipelineTemplateInstantiator';
import LogViewer from './components/LogViewer';
import { usePipelineStore } from './stores/pipelineStore';

// Create a client
const queryClient = new QueryClient();

const PipelineTemplateInstantiatorWrapper: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const handleInstantiate = (config: any) => {
    // Handle the instantiated pipeline config
    console.log('Instantiated config:', config);
    navigate('/');
  };

  const handleCancel = () => {
    navigate('/templates');
  };

  if (!id) {
    return null;
  }

  return (
    <PipelineTemplateInstantiator
      templateId={id}
      onInstantiate={handleInstantiate}
      onCancel={handleCancel}
    />
  );
};

const App: React.FC = () => {
  // Temporarily simplify the app to debug the blank page issue
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <SnackbarProvider maxSnack={3}>
          <div style={{ padding: '20px' }}>
            <h1>ChainOps Frontend - Debug Mode</h1>
            <p>If you can see this, React is working!</p>
            <p>Backend URL: {process.env.REACT_APP_API_URL || 'http://localhost:4001'}</p>
            <p>Environment: {process.env.NODE_ENV}</p>
            <button onClick={() => console.log('Button clicked!')}>Test Button</button>
          </div>
        </SnackbarProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App; 