import { jest } from '@jest/globals';

// Mock environment variables for testing
const testEnv = {
  // Core environment
  NODE_ENV: 'test',
  
  // Server configuration
  PORT: '3000',
  HOST: '127.0.0.1',
  
  // Authentication
  JWT_SECRET: 'test-secret',
  JWT_EXPIRES_IN: '1h',
  
  // Database (use in-memory or test database)
  DATABASE_URL: 'postgresql://test:test@localhost:5432/test_db',
  
  // Redis (use a separate database for tests)
  REDIS_URL: 'redis://localhost:6379/1',
  
  // Storage
  MINIO_ENDPOINT: 'localhost',
  MINIO_PORT: '9000',
  MINIO_ACCESS_KEY: 'test-access-key',
  MINIO_SECRET_KEY: 'test-secret-key',
  
  // Logging
  LOG_LEVEL: 'error', // Reduce noise during tests
  
  // Rate limiting
  RATE_LIMIT_WINDOW_MS: '60000',
  RATE_LIMIT_MAX_REQUESTS: '100',
  
  // CORS
  CORS_ORIGIN: 'http://localhost:3000'
};

// Apply all test environment variables
Object.entries(testEnv).forEach(([key, value]) => {
  Object.defineProperty(process.env, key, { value });
});

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise during tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = (...args) => {
  // Only log actual test failures, not expected errors from the application
  if (args[0] && typeof args[0] === 'string' && args[0].includes('FAIL')) {
    originalConsoleError(...args);
  }
};

console.warn = (...args) => {
  // Suppress warnings during tests
  // Uncomment the next line to see warnings during test development
  // originalConsoleWarn(...args);
};

// Restore console methods after tests
// Note: Individual test files should handle cleanup if needed
// afterAll(() => {
//   console.error = originalConsoleError;
//   console.warn = originalConsoleWarn;
// });