import { LogManager } from '../logs';
import { join } from 'path';

// Mock fs modules
jest.mock('fs', () => ({
  createReadStream: jest.fn(),
  createWriteStream: jest.fn(() => ({
    write: jest.fn(),
    end: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
    emit: jest.fn()
  }))
}));

jest.mock('fs/promises', () => ({
  mkdir: jest.fn().mockResolvedValue(undefined),
  readFile: jest.fn(),
  writeFile: jest.fn().mockResolvedValue(undefined),
  access: jest.fn().mockResolvedValue(undefined)
}));

jest.mock('stream/promises', () => ({
  pipeline: jest.fn().mockImplementation(async (source, dest) => {
    // Mock implementation to simulate writing to a file
    if (typeof source === 'string') {
      mockFileContents[mockCurrentFile] = source;
    }
    return Promise.resolve();
  })
}));

// Mock file system storage
const mockFileContents: Record<string, string> = {};
let mockCurrentFile = '';

describe('LogManager', () => {
    const testJobId = 'test-job-1';
    const testMessage = 'Test log message';
    let logManager: LogManager;

    beforeEach(async () => {
        // Reset mock file contents
        Object.keys(mockFileContents).forEach(key => delete mockFileContents[key]);
        
        // Setup mock for readFile to return our mock file contents
        const { readFile } = require('fs/promises');
        readFile.mockImplementation((path: string) => {
            mockCurrentFile = path;
            return Promise.resolve(mockFileContents[path] || '');
        });
        
        // Setup mock for createReadStream
        const { createReadStream } = require('fs');
        createReadStream.mockImplementation(() => {
            const { Readable } = require('stream');
            const readable = new Readable();
            readable._read = () => {};
            
            // Push mock file content to the stream
            if (mockFileContents[mockCurrentFile]) {
                readable.push(mockFileContents[mockCurrentFile]);
            }
            readable.push(null); // End the stream
            
            return readable;
        });
        
        logManager = LogManager.getInstance();
    });

    afterEach(async () => {
        jest.clearAllMocks();
    });

    it('should write job logs to a file', async () => {
        // Setup pipeline mock to capture the log entry
        const { pipeline } = require('stream/promises');
        pipeline.mockImplementation(async (source, dest) => {
            if (typeof source === 'string') {
                const logPath = join(process.cwd(), 'logs', 'jobs', `${testJobId}.log`);
                mockCurrentFile = logPath;
                mockFileContents[logPath] = source;
            }
            return Promise.resolve();
        });
        
        await logManager.writeJobLog(testJobId, testMessage);

        const logPath = join(process.cwd(), 'logs', 'jobs', `${testJobId}.log`);
        expect(mockFileContents[logPath]).toBeDefined();
        
        const logEntry = JSON.parse(mockFileContents[logPath]);
        expect(logEntry.message).toBe(testMessage);
        expect(logEntry.jobId).toBe(testJobId);
        expect(logEntry.level).toBe('info');
        expect(logEntry.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    });

    it('should append multiple log messages', async () => {
        const messages = ['First message', 'Second message', 'Third message'];
        const logPath = join(process.cwd(), 'logs', 'jobs', `${testJobId}.log`);
        mockCurrentFile = logPath;
        mockFileContents[logPath] = '';
        
        // Setup pipeline mock to append log entries
        const { pipeline } = require('stream/promises');
        pipeline.mockImplementation(async (source, dest) => {
            if (typeof source === 'string') {
                mockFileContents[logPath] += source;
            }
            return Promise.resolve();
        });
        
        for (const message of messages) {
            await logManager.writeJobLog(testJobId, message);
        }

        expect(mockFileContents[logPath]).toBeDefined();
        
        for (const message of messages) {
            expect(mockFileContents[logPath]).toContain(message);
        }
    });

    it('should get log stats', async () => {
        const logPath = join(process.cwd(), 'logs', 'jobs', `${testJobId}.log`);
        mockCurrentFile = logPath;
        
        // Create mock log entries
        const logEntries = [
            JSON.stringify({
                timestamp: new Date().toISOString(),
                level: 'info',
                message: 'Info message',
                jobId: testJobId
            }) + '\n',
            JSON.stringify({
                timestamp: new Date().toISOString(),
                level: 'error',
                message: 'Error message',
                jobId: testJobId
            }) + '\n',
            JSON.stringify({
                timestamp: new Date().toISOString(),
                level: 'warning',
                message: 'Warning message',
                jobId: testJobId
            }) + '\n'
        ];
        
        mockFileContents[logPath] = logEntries.join('');
        
        // Setup createReadStream mock to return our mock log entries
        const { createReadStream } = require('fs');
        createReadStream.mockImplementation(() => {
            const { Readable } = require('stream');
            const readable = new Readable();
            readable._read = () => {};
            readable.push(mockFileContents[logPath]);
            readable.push(null);
            return readable;
        });
        
        const stats = await logManager.getLogStats(testJobId);
        expect(stats.totalEntries).toBe(3);
        expect(stats.errorCount).toBe(1);
        expect(stats.warningCount).toBe(1);
        expect(stats.infoCount).toBe(1);
    });
});