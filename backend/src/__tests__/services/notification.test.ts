// Mock all external dependencies before importing the service
jest.mock('@slack/web-api', () => ({
  WebClient: jest.fn(() => ({
    chat: {
      postMessage: jest.fn().mockResolvedValue({ ok: true }),
    },
  })),
}));

jest.mock('@slack/webhook', () => ({
  IncomingWebhook: jest.fn(() => ({
    send: jest.fn().mockResolvedValue({ text: 'ok' }),
  })),
}));

jest.mock('discord.js', () => ({
  Client: jest.fn(() => ({
    channels: {
      cache: {
        get: jest.fn().mockReturnValue({
          send: jest.fn().mockResolvedValue({ id: 'test-message-id' }),
        }),
      },
    },
    login: jest.fn().mockResolvedValue(undefined),
  })),
}));

jest.mock('nodemailer', () => ({
  createTransporter: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  })),
}));

jest.mock('twilio', () => jest.fn(() => ({
  messages: {
    create: jest.fn().mockResolvedValue({ sid: 'test-sid' }),
  },
})));

import { NotificationService, NotificationPayload } from '../../services/notification';
import { NotificationEvent, NotificationChannelType } from '../../types/notifications';
import { logger } from '../../utils/logger';

// Mock all dependencies
jest.mock('../../services/database', () => ({
  DatabaseService: {
    getClient: jest.fn(() => ({
      notificationChannel: {
        findUnique: jest.fn(),
      },
      notificationLog: {
        create: jest.fn(),
        update: jest.fn(),
      },
    })),
  },
}));

jest.mock('../../utils/logger', () => ({
  logger: {
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock nodemailer
jest.mock('nodemailer', () => ({
  createTransport: jest.fn(() => ({
    sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
  })),
}));

// Mock axios for webhook notifications
jest.mock('axios', () => ({
  post: jest.fn().mockResolvedValue({ status: 200 }),
}));

// Mock WebSocketService
jest.mock('../../services/socket', () => ({
  WebSocketService: jest.fn(() => ({
    emit: jest.fn(),
    broadcast: jest.fn(),
  })),
}));

// Set test environment
process.env.NODE_ENV = 'test';

describe('NotificationService', () => {
  let notificationService: NotificationService;
  let mockDb: any;
  let mockWebSocketService: any;

  beforeEach(() => {
    // Reset the singleton instance
    (NotificationService as any).instance = undefined;

    // Create mock WebSocketService
    const { WebSocketService } = require('../../services/socket');
    mockWebSocketService = new WebSocketService();

    notificationService = NotificationService.getInstance(mockWebSocketService);
    const { DatabaseService } = require('../../services/database');
    mockDb = DatabaseService.getClient();
    jest.clearAllMocks();
  });

  describe('sendNotification', () => {
    it('should send notification successfully', async () => {
      const mockChannel = {
        id: 'channel-1',
        type: NotificationChannelType.EMAIL,
        isActive: true,
        config: {
          host: 'smtp.test.com',
          port: 587,
          secure: false,
          auth: { user: 'test', pass: 'test' },
          from: '<EMAIL>',
          to: ['<EMAIL>']
        }
      };

      const mockLogEntry = { 
        id: 'log-1',
        status: 'PENDING',
        channelId: 'channel-1',
        event: NotificationEvent.PIPELINE_COMPLETED
      };

      mockDb.notificationChannel.findUnique.mockResolvedValue(mockChannel);
      mockDb.notificationLog.create.mockResolvedValue(mockLogEntry);
      mockDb.notificationLog.update.mockResolvedValue({
        ...mockLogEntry,
        status: 'SENT'
      });

      const payload: NotificationPayload = {
        event: NotificationEvent.PIPELINE_COMPLETED,
        title: 'Test Notification',
        message: 'Test message',
        projectId: 'project-1'
      };

      const result = await notificationService.sendNotification('channel-1', payload);

      expect(result.success).toBe(true);
      expect(mockDb.notificationChannel.findUnique).toHaveBeenCalledWith({
        where: { id: 'channel-1' },
        include: { project: true, user: true }
      });
      expect(mockDb.notificationLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          channelId: 'channel-1',
          event: NotificationEvent.PIPELINE_COMPLETED,
          status: 'PENDING'
        })
      });
      expect(mockDb.notificationLog.update).toHaveBeenCalledWith({
        where: { id: 'log-1' },
        data: { status: 'SENT' }
      });
    });

    it('should handle inactive channel', async () => {
      const mockChannel = {
        id: 'channel-1',
        type: NotificationChannelType.EMAIL,
        isActive: false,
        config: {
          host: 'smtp.test.com',
          port: 587,
          secure: false,
          auth: { user: 'test', pass: 'test' },
          from: '<EMAIL>',
          to: ['<EMAIL>']
        },
        project: null,
        user: null
      };

      // Set up the mock after clearAllMocks
      mockDb.notificationChannel.findUnique.mockResolvedValue(mockChannel);

      const payload: NotificationPayload = {
        event: NotificationEvent.PIPELINE_COMPLETED,
        title: 'Test Notification',
        message: 'Test message'
      };

      const result = await notificationService.sendNotification('channel-1', payload);

      // Debug: Check what the mock was called with
      expect(mockDb.notificationChannel.findUnique).toHaveBeenCalledWith({
        where: { id: 'channel-1' },
        include: { project: true, user: true }
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Channel not found or inactive');
      expect(logger.warn).toHaveBeenCalledWith('Attempted to send notification to inactive channel', {
        channelId: 'channel-1'
      });
    });

    it('should handle channel not found', async () => {
      mockDb.notificationChannel.findUnique.mockResolvedValue(null);

      const payload: NotificationPayload = {
        event: NotificationEvent.PIPELINE_COMPLETED,
        title: 'Test Notification',
        message: 'Test message'
      };

      const result = await notificationService.sendNotification('channel-1', payload);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Channel not found or inactive');
      expect(logger.warn).toHaveBeenCalledWith('Attempted to send notification to non-existent channel', {
        channelId: 'channel-1'
      });
    });

    it('should handle notification sending failure', async () => {
      const mockChannel = {
        id: 'channel-1',
        type: NotificationChannelType.EMAIL,
        isActive: true,
        config: {
          host: 'smtp.test.com',
          port: 587,
          secure: false,
          auth: { user: 'test', pass: 'test' },
          from: '<EMAIL>',
          to: ['<EMAIL>']
        }
      };

      const mockLogEntry = { 
        id: 'log-1',
        status: 'PENDING',
        channelId: 'channel-1',
        event: NotificationEvent.PIPELINE_COMPLETED
      };

      mockDb.notificationChannel.findUnique.mockResolvedValue(mockChannel);
      mockDb.notificationLog.create.mockResolvedValue(mockLogEntry);
      mockDb.notificationLog.update.mockRejectedValue(new Error('Failed to send notification'));

      const payload: NotificationPayload = {
        event: NotificationEvent.PIPELINE_COMPLETED,
        title: 'Test Notification',
        message: 'Test message',
        projectId: 'project-1'
      };

      const result = await notificationService.sendNotification('channel-1', payload);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to send notification');
      expect(logger.error).toHaveBeenCalledWith('Failed to send notification', {
        channelId: 'channel-1',
        error: expect.any(Error)
      });
      expect(mockDb.notificationLog.update).toHaveBeenCalledWith({
        where: { id: 'log-1' },
        data: { status: 'FAILED' }
      });
    });
  });
});