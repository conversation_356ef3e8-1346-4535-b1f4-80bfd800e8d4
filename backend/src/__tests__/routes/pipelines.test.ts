import request from 'supertest';
import express from 'express';

// Mock database first
const mockPipelines = [
  {
    id: 'pipeline-1',
    name: 'Test Pipeline',
    slug: 'test-pipeline',
    description: 'Test pipeline description',
    projectId: 'c123456789012345678901234',
    config: {
      jobs: {
        test: {
          steps: [
            { name: 'Test Step', run: 'echo "test"' },
          ],
        },
      },
    },
    status: 'active',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    project: {
      id: 'c123456789012345678901234',
      name: 'Test Project',
      slug: 'test-project',
    },
    user: {
      id: 'user-1',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User',
      avatar: null,
    },
    runs: [],
    _count: {
      runs: 0,
    },
  },
];

const mockPrisma = {
  pipeline: {
    findMany: jest.fn().mockResolvedValue(mockPipelines),
    findUnique: jest.fn().mockResolvedValue(mockPipelines[0]),
    create: jest.fn().mockResolvedValue(mockPipelines[0]),
    update: jest.fn().mockResolvedValue(mockPipelines[0]),
    delete: jest.fn().mockResolvedValue(mockPipelines[0]),
    count: jest.fn().mockResolvedValue(1),
  },
  projectMember: {
    findFirst: jest.fn().mockResolvedValue({ id: 'member-1' }),
  },
};

jest.mock('../../services/database', () => ({
  prisma: mockPrisma,
  DatabaseService: {
    getClient: jest.fn(() => mockPrisma),
  },
}));

// Mock auth middleware
jest.mock('../../middleware/auth', () => ({
  requireAuth: (req: any, res: any, next: any) => {
    req.user = { id: 'user-1', role: 'USER' };
    next();
  },
}));

// Mock queue service
jest.mock('../../services/queue', () => ({
  QueueService: {
    getInstance: jest.fn(() => ({
      enqueue: jest.fn().mockResolvedValue({ id: 'job-1' }),
    })),
  },
}));

// Import routes after mocks
import pipelinesRoutes from '../../routes/pipelines';
import { CustomError } from '../../middleware/errorHandler';

const app = express();
app.use(express.json());

// Add mock auth middleware
app.use((req: any, res: any, next: any) => {
  req.user = { id: 'user-1', role: 'USER' };
  next();
});

app.use('/', pipelinesRoutes);

describe('Pipelines Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /', () => {
    it('should list pipelines with pagination', async () => {
      const response = await request(app)
        .get('/')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.pipelines).toEqual(mockPipelines);
      expect(response.body.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        pages: 1,
      });
    });

    it('should filter pipelines by search term', async () => {
      const response = await request(app)
        .get('/')
        .query({ search: 'test' });

      expect(response.status).toBe(200);
      expect(mockPrisma.pipeline.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({ name: expect.any(Object) }),
              expect.objectContaining({ description: expect.any(Object) }),
            ]),
          }),
        })
      );
    });

    it('should filter pipelines by project', async () => {
      const response = await request(app)
        .get('/')
        .query({ projectId: 'c123456789012345678901234' });

      expect(response.status).toBe(200);
      expect(mockPrisma.pipeline.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            projectId: 'c123456789012345678901234',
          }),
        })
      );
    });

    it('should filter pipelines by status', async () => {
      const response = await request(app)
        .get('/')
        .query({ status: 'active' });

      expect(response.status).toBe(200);
      expect(mockPrisma.pipeline.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'active',
          }),
        })
      );
    });
  });

  describe('POST /', () => {
    it('should create pipeline successfully', async () => {
      const pipelineData = {
        name: 'New Pipeline',
        slug: 'new-pipeline',
        description: 'New pipeline description',
        projectId: 'c123456789012345678901234',
        config: {
          jobs: {
            test: {
              steps: [
                { name: 'Test Step', run: 'echo "test"' },
              ],
            },
          },
        },
      };

      const response = await request(app)
        .post('/')
        .send(pipelineData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(mockPrisma.pipeline.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            name: pipelineData.name,
            slug: pipelineData.slug,
            projectId: pipelineData.projectId,
          }),
        })
      );
    });

    it('should validate pipeline name', async () => {
      const response = await request(app)
        .post('/')
        .send({
          name: '',
          slug: 'test-pipeline',
          projectId: 'c123456789012345678901234',
          config: {},
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should validate pipeline slug format', async () => {
      const response = await request(app)
        .post('/')
        .send({
          name: 'Test Pipeline',
          slug: 'invalid slug',
          projectId: 'c123456789012345678901234',
          config: {},
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should validate pipeline configuration', async () => {
      const response = await request(app)
        .post('/')
        .send({
          name: 'Test Pipeline',
          slug: 'test-pipeline',
          projectId: 'c123456789012345678901234',
          config: 'invalid config',
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /:id', () => {
    it('should get pipeline by id', async () => {
      const response = await request(app).get('/pipeline-1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.pipeline).toEqual(mockPipelines[0]);
    });

    it('should return 404 for non-existent pipeline', async () => {
      mockPrisma.pipeline.findUnique.mockResolvedValueOnce(null);

      const response = await request(app).get('/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /:id', () => {
    it('should update pipeline successfully', async () => {
      const updateData = {
        name: 'Updated Pipeline',
        description: 'Updated description',
      };

      const response = await request(app)
        .put('/pipeline-1')
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockPrisma.pipeline.update).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: 'pipeline-1' },
          data: updateData,
        })
      );
    });

    it('should validate update data', async () => {
      const response = await request(app)
        .put('/pipeline-1')
        .send({ name: '' });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /:id', () => {
    it('should delete pipeline successfully', async () => {
      const response = await request(app).delete('/pipeline-1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockPrisma.pipeline.delete).toHaveBeenCalledWith({
        where: { id: 'pipeline-1' },
      });
    });

    it('should return 404 for non-existent pipeline', async () => {
      mockPrisma.pipeline.findUnique.mockResolvedValueOnce(null);

      const response = await request(app).delete('/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });
});