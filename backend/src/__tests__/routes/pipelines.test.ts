import request from 'supertest';
import express from 'express';

// Mock database first
const mockPipelines = [
  {
    id: 'pipeline-1',
    name: 'Test Pipeline',
    description: 'Test pipeline description',
    projectId: 'c123456789012345678901234',
    config: {
      jobs: {
        test: {
          steps: [
            { name: 'Test Step', run: 'echo "test"' },
          ],
        },
      },
    },
    status: 'active',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    project: {
      id: 'c123456789012345678901234',
      name: 'Test Project',
      slug: 'test-project',
    },
    triggers: [],
    runs: [],
    _count: {
      runs: 0,
    },
  },
];

const mockPrisma = {
  pipeline: {
    findMany: jest.fn().mockResolvedValue(mockPipelines),
    findUnique: jest.fn().mockResolvedValue(mockPipelines[0]),
    findFirst: jest.fn().mockResolvedValue(mockPipelines[0]),
    create: jest.fn().mockResolvedValue(mockPipelines[0]),
    update: jest.fn().mockResolvedValue(mockPipelines[0]),
    delete: jest.fn().mockResolvedValue(mockPipelines[0]),
    count: jest.fn().mockResolvedValue(1),
  },
  projectMember: {
    findFirst: jest.fn().mockResolvedValue({
      id: 'member-1',
      userId: 'user-1',
      projectId: 'c123456789012345678901234',
      role: 'OWNER'
    }),
  },
  pipelineRun: {
    findFirst: jest.fn().mockResolvedValue({ number: 1 }),
    create: jest.fn().mockResolvedValue({
      id: 'run-1',
      number: 2,
      pipelineId: 'pipeline-1',
      status: 'PENDING',
    }),
  },
};

jest.mock('../../services/database', () => ({
  prisma: mockPrisma,
  DatabaseService: {
    getClient: jest.fn(() => mockPrisma),
  },
}));

// Mock auth middleware
jest.mock('../../middleware/auth', () => ({
  requireAuth: (req: any, res: any, next: any) => {
    req.user = { id: 'user-1', role: 'USER' };
    next();
  },
}));

// Mock queue service
jest.mock('../../services/queue', () => ({
  QueueService: {
    getInstance: jest.fn(() => ({
      enqueue: jest.fn().mockResolvedValue({ id: 'job-1' }),
    })),
    addNotification: jest.fn().mockResolvedValue({ id: 'notification-1' }),
    addPipelineJob: jest.fn().mockResolvedValue({ id: 'pipeline-job-1' }),
  },
}));

// Import routes after mocks
import pipelinesRoutes from '../../routes/pipelines';
import { CustomError } from '../../middleware/errorHandler';

// Mock global fetch
global.fetch = jest.fn().mockResolvedValue({
  ok: true,
  json: jest.fn().mockResolvedValue({
    data: { secrets: { SECRET_KEY: 'test-value' } }
  }),
}) as jest.MockedFunction<typeof fetch>;

const app = express();
app.use(express.json());

// Add mock auth middleware
app.use((req: any, res: any, next: any) => {
  req.user = { id: 'user-1', role: 'USER' };
  next();
});

app.use('/', pipelinesRoutes);

// Add error handling middleware
app.use((error: any, req: any, res: any, next: any) => {
  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || 'Internal server error',
  });
});

describe('Pipelines Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Reset mocks to default successful state
    mockPrisma.pipeline.findMany.mockResolvedValue(mockPipelines);
    mockPrisma.pipeline.findUnique.mockResolvedValue(mockPipelines[0]);
    mockPrisma.pipeline.findFirst.mockResolvedValue(null); // Default to no existing pipeline
    mockPrisma.pipeline.create.mockResolvedValue(mockPipelines[0]);
    mockPrisma.pipeline.update.mockResolvedValue(mockPipelines[0]);
    mockPrisma.pipeline.delete.mockResolvedValue(mockPipelines[0]);
    mockPrisma.pipeline.count.mockResolvedValue(1);
    mockPrisma.projectMember.findFirst.mockResolvedValue({
      id: 'member-1',
      userId: 'user-1',
      projectId: 'c123456789012345678901234',
      role: 'OWNER'
    });
  });

  describe('GET /', () => {
    it('should list pipelines with pagination', async () => {
      const response = await request(app)
        .get('/')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.pipelines).toEqual(mockPipelines);
      expect(response.body.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 1,
        pages: 1,
      });
    });

    it('should filter pipelines by search term', async () => {
      const response = await request(app)
        .get('/')
        .query({ search: 'test' });

      expect(response.status).toBe(200);
      expect(mockPrisma.pipeline.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              expect.objectContaining({ name: expect.any(Object) }),
              expect.objectContaining({ description: expect.any(Object) }),
            ]),
          }),
        })
      );
    });

    it('should filter pipelines by project', async () => {
      const response = await request(app)
        .get('/')
        .query({ projectId: 'c123456789012345678901234' });

      expect(response.status).toBe(200);
      expect(mockPrisma.pipeline.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            projectId: 'c123456789012345678901234',
          }),
        })
      );
    });

    it('should filter pipelines by status', async () => {
      const response = await request(app)
        .get('/')
        .query({ status: 'active' });

      expect(response.status).toBe(200);
      expect(mockPrisma.pipeline.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'active',
          }),
        })
      );
    });
  });

  describe('POST /', () => {
    it('should create pipeline successfully', async () => {
      const pipelineData = {
        name: 'New Pipeline',
        description: 'New pipeline description',
        projectId: 'c123456789012345678901234',
        config: {
          jobs: {
            test: {
              steps: [
                { name: 'Test Step', run: 'echo "test"' },
              ],
            },
          },
        },
      };

      const response = await request(app)
        .post('/')
        .send(pipelineData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(mockPrisma.pipeline.create).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            name: pipelineData.name,
            description: pipelineData.description,
            projectId: pipelineData.projectId,
          }),
        })
      );
    });

    it('should validate pipeline name', async () => {
      const response = await request(app)
        .post('/')
        .send({
          name: '',
          projectId: 'c123456789012345678901234',
          config: {},
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should validate pipeline project ID format', async () => {
      const response = await request(app)
        .post('/')
        .send({
          name: 'Test Pipeline',
          projectId: 'invalid-project-id',
          config: {},
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });

    it('should validate pipeline configuration', async () => {
      const response = await request(app)
        .post('/')
        .send({
          name: 'Test Pipeline',
          projectId: 'c123456789012345678901234',
          config: 'invalid config',
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /:id', () => {
    it('should get pipeline by id', async () => {
      // Mock findFirst to return the pipeline for this specific test
      mockPrisma.pipeline.findFirst.mockResolvedValueOnce(mockPipelines[0]);

      const response = await request(app).get('/pipeline-1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.pipeline).toEqual(mockPipelines[0]);
    });

    it('should return 404 for non-existent pipeline', async () => {
      mockPrisma.pipeline.findFirst.mockResolvedValueOnce(null);

      const response = await request(app).get('/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });

  describe('PUT /:id', () => {
    it('should update pipeline successfully', async () => {
      // Mock findFirst to return the pipeline for this specific test
      mockPrisma.pipeline.findFirst.mockResolvedValueOnce(mockPipelines[0]);

      const updateData = {
        name: 'Updated Pipeline',
        description: 'Updated description',
      };

      const response = await request(app)
        .put('/pipeline-1')
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockPrisma.pipeline.update).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: 'pipeline-1' },
          data: expect.objectContaining(updateData),
        })
      );
    });

    it('should validate update data', async () => {
      const response = await request(app)
        .put('/pipeline-1')
        .send({ name: '' });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('DELETE /:id', () => {
    it('should delete pipeline successfully', async () => {
      // Mock findFirst to return the pipeline for this specific test
      mockPrisma.pipeline.findFirst.mockResolvedValueOnce(mockPipelines[0]);

      const response = await request(app).delete('/pipeline-1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(mockPrisma.pipeline.delete).toHaveBeenCalledWith({
        where: { id: 'pipeline-1' },
      });
    });

    it('should return 404 for non-existent pipeline', async () => {
      mockPrisma.pipeline.findFirst.mockResolvedValueOnce(null);

      const response = await request(app).delete('/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
    });
  });
});