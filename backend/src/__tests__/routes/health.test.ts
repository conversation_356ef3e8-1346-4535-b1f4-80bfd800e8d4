import request from 'supertest';
import express from 'express';
import healthRoutes from '../../routes/health';

const app = express();
app.use(express.json());
app.use('/', healthRoutes);

// Mock database service
jest.mock('../../services/database', () => ({
  DatabaseService: {
    getInstance: jest.fn(() => ({
      getClient: jest.fn(() => ({
        $queryRaw: jest.fn().mockResolvedValue([{ result: 1 }]),
      })),
    })),
    healthCheck: jest.fn().mockResolvedValue(true),
  },
}));

// Mock Queue service
jest.mock('../../services/queue', () => ({
  QueueService: {
    healthCheck: jest.fn().mockResolvedValue(true),
  },
}));

// Mock MinIO service
jest.mock('../../services/minio', () => ({
  MinioService: {
    healthCheck: jest.fn().mockResolvedValue(true),
  },
}));

describe('Health Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset mocks to default successful state
    const mockDatabase = require('../../services/database');
    const mockQueue = require('../../services/queue');
    const mockMinio = require('../../services/minio');

    mockDatabase.DatabaseService.healthCheck.mockResolvedValue(true);
    mockQueue.QueueService.healthCheck.mockResolvedValue(true);
    mockMinio.MinioService.healthCheck.mockResolvedValue(true);
  });

  describe('GET /health', () => {
    it('should return health status successfully', async () => {
      const response = await request(app).get('/');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.status).toBe('healthy');
      expect(response.body.timestamp).toBeDefined();
      expect(response.body.uptime).toBeDefined();
      expect(response.body.version).toBeDefined();
    });
  });

  describe('GET /health/detailed', () => {
    it('should return detailed health status successfully', async () => {
      const response = await request(app).get('/detailed');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.status).toBe('healthy');
      expect(response.body.services).toBeDefined();
      expect(response.body.services.database).toBeDefined();
      expect(response.body.services.queue).toBeDefined();
      expect(response.body.services.storage).toBeDefined();
    });

    it('should handle service failures gracefully', async () => {
      // Mock database failure
      const mockDatabase = require('../../services/database');
      mockDatabase.DatabaseService.healthCheck.mockResolvedValue(false);

      const response = await request(app).get('/detailed');

      expect(response.status).toBe(503);
      expect(response.body.success).toBe(false);
      expect(response.body.status).toBe('unhealthy');
      expect(response.body.services.database).toBe(false);
    });
  });

  describe('GET /health/readiness', () => {
    it('should return readiness status successfully', async () => {
      const response = await request(app).get('/readiness');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.ready).toBe(true);
    });

    it('should return not ready when services are down', async () => {
      // Mock database failure
      const mockDatabase = require('../../services/database');
      mockDatabase.DatabaseService.healthCheck.mockResolvedValue(false);

      const response = await request(app).get('/readiness');

      expect(response.status).toBe(503);
      expect(response.body.success).toBe(false);
      expect(response.body.ready).toBe(false);
    });
  });

  describe('GET /health/liveness', () => {
    it('should return liveness status successfully', async () => {
      const response = await request(app).get('/liveness');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.alive).toBe(true);
    });
  });
});
