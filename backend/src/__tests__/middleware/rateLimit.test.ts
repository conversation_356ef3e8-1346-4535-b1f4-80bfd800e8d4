import { RateLimit } from '../../middleware/ratelimit';
import { Request, Response, NextFunction } from 'express';
import { logger } from '../../utils/logger';

jest.mock('../../utils/logger', () => ({
  logger: {
    warn: jest.fn(),
    error: jest.fn(),
  },
}));

describe('RateLimit', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction;
  let rateLimiters: RateLimit[] = [];

  beforeEach(() => {
    mockRequest = {
      ip: '127.0.0.1',
      get: jest.fn().mockReturnValue('test-agent'),
      path: '/test',
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      send: jest.fn(),
      setHeader: jest.fn(),
    };
    nextFunction = jest.fn();
    jest.clearAllMocks();
  });
  
  afterEach(() => {
    // Clean up all rate limiters created during tests
    RateLimit.destroyAll();
  });
  
  afterAll(() => {
    // Make sure all intervals are cleared
    RateLimit.destroyAll();
  });

  describe('RateLimit Creation', () => {
    it('should create rate limiter with default options', () => {
      const rateLimiter = RateLimit.create({
        windowMs: 60000,
        max: 100,
      });
      expect(rateLimiter).toBeDefined();
    });

    it('should create rate limiter with custom options', () => {
      const rateLimiter = RateLimit.create({
        windowMs: 30000,
        max: 50,
        message: 'Custom message',
        statusCode: 418,
        skipSuccessfulRequests: true,
        skipFailedRequests: true,
        keyGenerator: (req) => req.ip + '-custom',
      });
      expect(rateLimiter).toBeDefined();
    });
  });

  describe('Predefined Rate Limiters', () => {
    it('should create strict rate limiter', () => {
      const rateLimiter = RateLimit.strict();
      expect(rateLimiter).toBeDefined();
    });

    it('should create moderate rate limiter', () => {
      const rateLimiter = RateLimit.moderate();
      expect(rateLimiter).toBeDefined();
    });

    it('should create lenient rate limiter', () => {
      const rateLimiter = RateLimit.lenient();
      expect(rateLimiter).toBeDefined();
    });

    it('should create auth rate limiter', () => {
      const rateLimiter = RateLimit.auth();
      expect(rateLimiter).toBeDefined();
    });

    it('should create API rate limiter', () => {
      const rateLimiter = RateLimit.api();
      expect(rateLimiter).toBeDefined();
    });
  });

  describe('Rate Limiting Behavior', () => {
    it('should allow requests within limit', () => {
      const rateLimiter = RateLimit.create({
        windowMs: 60000,
        max: 2,
      });
      const middleware = rateLimiter.middleware();

      // First request
      middleware(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1);
      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-RateLimit-Limit', 2);
      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-RateLimit-Remaining', 1);

      // Second request
      middleware(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(2);
      expect(mockResponse.setHeader).toHaveBeenCalledWith('X-RateLimit-Remaining', 0);
    });

    it('should block requests exceeding limit', () => {
      const rateLimiter = RateLimit.create({
        windowMs: 60000,
        max: 1,
      });
      const middleware = rateLimiter.middleware();

      // First request (allowed)
      middleware(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1);

      // Second request (blocked)
      middleware(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1); // Still 1 because second request was blocked
      expect(mockResponse.status).toHaveBeenCalledWith(429);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.any(String),
          retryAfter: expect.any(Number),
        })
      );
      expect(logger.warn).toHaveBeenCalledWith('Rate limit exceeded', expect.any(Object));
    });

    it('should handle requests with custom key generator', () => {
      const rateLimiter = RateLimit.create({
        windowMs: 60000,
        max: 1,
        keyGenerator: (req) => req.path,
      });
      const middleware = rateLimiter.middleware();

      // First request to /test
      middleware(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1);

      // Second request to /test (blocked)
      middleware(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).toHaveBeenCalledWith(429);

      // Request to different path (allowed)
      const differentPathRequest = {
        ...mockRequest,
        path: '/different',
      };
      middleware(differentPathRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(2);
    });

    it('should reset counter after window expires', () => {
      jest.useFakeTimers();
      const rateLimiter = RateLimit.create({
        windowMs: 1000,
        max: 1,
      });
      const middleware = rateLimiter.middleware();

      // First request
      middleware(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1);

      // Second request (blocked)
      middleware(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1);

      // Advance time past window
      jest.advanceTimersByTime(1001);

      // Third request (allowed after window reset)
      middleware(mockRequest as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(2);

      jest.useRealTimers();
    });

    it('should handle requests without IP', () => {
      const rateLimiter = RateLimit.create({
        windowMs: 60000,
        max: 1,
      });
      const middleware = rateLimiter.middleware();

      const requestWithoutIp = {
        ...mockRequest,
        ip: undefined,
      };

      middleware(requestWithoutIp as Request, mockResponse as Response, nextFunction);
      expect(nextFunction).toHaveBeenCalledTimes(1);
    });
  });
});