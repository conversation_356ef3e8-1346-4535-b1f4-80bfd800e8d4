import Bull, { Queue, Job } from 'bull';
import Redis from 'ioredis';
import { logger } from '../utils/logger';

export interface JobData {
  id: string;
  type: string;
  payload: any;
  userId: string;
  projectId?: string;
  pipelineId?: string;
  runId?: string;
}

class QueueService {
  private static instance: QueueService;
  private redis: Redis;
  private pipelineQueue: Queue;
  private jobQueue: Queue;
  private notificationQueue: Queue;

  private constructor() {
    // Initialize Redis connection with better error handling
    this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6380', {
      retryStrategy: (times) => {
        // Stop retrying after 3 attempts
        if (times > 3) {
          return null;
        }
        return Math.min(times * 100, 1000);
      },
      enableReadyCheck: false,
      maxRetriesPerRequest: 3,
      connectTimeout: 5000,
      lazyConnect: true,
    });

    // Handle Redis connection errors gracefully
    this.redis.on('error', (error) => {
      logger.warn('Redis connection error (will continue without Redis):', error.message);
    });

    this.redis.on('connect', () => {
      logger.info('Redis connected successfully');
    });

    // Initialize queues with better error handling
    this.pipelineQueue = new Bull('pipeline-queue', {
      redis: {
        host: this.getRedisHost(),
        port: this.getRedisPort(),
        password: this.getRedisPassword(),
        connectTimeout: 5000,
        lazyConnect: true,
        maxRetriesPerRequest: 3,
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.jobQueue = new Bull('job-queue', {
      redis: {
        host: this.getRedisHost(),
        port: this.getRedisPort(),
        password: this.getRedisPassword(),
        connectTimeout: 5000,
        lazyConnect: true,
        maxRetriesPerRequest: 3,
      },
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.notificationQueue = new Bull('notification-queue', {
      redis: {
        host: this.getRedisHost(),
        port: this.getRedisPort(),
        password: this.getRedisPassword(),
        connectTimeout: 5000,
        lazyConnect: true,
        maxRetriesPerRequest: 3,
      },
      defaultJobOptions: {
        removeOnComplete: 50,
        removeOnFail: 25,
        attempts: 2,
        backoff: {
          type: 'fixed',
          delay: 5000,
        },
      },
    });

    this.setupEventHandlers();
  }

  private getRedisHost(): string {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const url = new URL(redisUrl);
    return url.hostname;
  }

  private getRedisPort(): number {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const url = new URL(redisUrl);
    return parseInt(url.port) || 6379;
  }

  private getRedisPassword(): string | undefined {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
    const url = new URL(redisUrl);
    return url.password || undefined;
  }

  private setupEventHandlers(): void {
    // Pipeline queue events
    this.pipelineQueue.on('completed', (job: Job) => {
      logger.info(`Pipeline job ${job.id} completed`);
    });

    this.pipelineQueue.on('failed', (job: Job, err: Error) => {
      logger.error(`Pipeline job ${job.id} failed:`, err);
    });

    // Job queue events
    this.jobQueue.on('completed', (job: Job) => {
      logger.info(`Job ${job.id} completed`);
    });

    this.jobQueue.on('failed', (job: Job, err: Error) => {
      logger.error(`Job ${job.id} failed:`, err);
    });

    // Notification queue events
    this.notificationQueue.on('completed', (job: Job) => {
      logger.info(`Notification job ${job.id} completed`);
    });

    this.notificationQueue.on('failed', (job: Job, err: Error) => {
      logger.error(`Notification job ${job.id} failed:`, err);
    });
  }

  public static getInstance(): QueueService {
    if (!QueueService.instance) {
      QueueService.instance = new QueueService();
    }
    return QueueService.instance;
  }

  public static async initialize(): Promise<void> {
    const instance = QueueService.getInstance();
    try {
      // Add timeout to Redis connection
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Redis connection timeout')), 5000);
      });

      const pingPromise = instance.redis.ping();

      await Promise.race([pingPromise, timeoutPromise]);
      logger.info('Queue service connected to Redis');
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
      throw error;
    }
  }

  public static async close(): Promise<void> {
    const instance = QueueService.getInstance();
    try {
      // Close queues only if they exist and are not already closed
      const closeQueue = async (queue: Queue, name: string) => {
        try {
          if (queue && queue.client.status !== 'end') {
            await queue.close();
            logger.info(`${name} queue closed successfully`);
          }
        } catch (err) {
          logger.warn(`Error closing ${name} queue:`, err);
          // Continue with other closures
        }
      };

      await closeQueue(instance.pipelineQueue, 'Pipeline');
      await closeQueue(instance.jobQueue, 'Job');
      await closeQueue(instance.notificationQueue, 'Notification');

      // Close Redis connection only if it's still connected
      if (instance.redis && instance.redis.status !== 'end') {
        await instance.redis.quit();
        logger.info('Redis connection closed successfully');
      }

      logger.info('Queue service disconnected');
    } catch (error) {
      logger.error('Error closing queue service:', error);
      // Don't throw error to allow graceful shutdown to continue
    }
  }

  // Pipeline queue methods
  public static async addPipelineJob(data: JobData, options?: Bull.JobOptions): Promise<Job> {
    const instance = QueueService.getInstance();
    return instance.pipelineQueue.add('execute-pipeline', data, options);
  }

  public static async addJob(data: JobData, options?: Bull.JobOptions): Promise<Job> {
    const instance = QueueService.getInstance();
    return instance.jobQueue.add('execute-job', data, options);
  }

  public static async addNotification(data: any, options?: Bull.JobOptions): Promise<Job> {
    const instance = QueueService.getInstance();
    return instance.notificationQueue.add('send-notification', data, options);
  }

  // Queue status methods
  public static async getPipelineQueueStats(): Promise<any> {
    const instance = QueueService.getInstance();
    return {
      waiting: await instance.pipelineQueue.getWaiting(),
      active: await instance.pipelineQueue.getActive(),
      completed: await instance.pipelineQueue.getCompleted(),
      failed: await instance.pipelineQueue.getFailed(),
    };
  }

  public static async getJobQueueStats(): Promise<any> {
    const instance = QueueService.getInstance();
    return {
      waiting: await instance.jobQueue.getWaiting(),
      active: await instance.jobQueue.getActive(),
      completed: await instance.jobQueue.getCompleted(),
      failed: await instance.jobQueue.getFailed(),
    };
  }

  // Health check
  public static async healthCheck(): Promise<boolean> {
    try {
      const instance = QueueService.getInstance();
      await instance.redis.ping();
      return true;
    } catch (error) {
      logger.error('Queue service health check failed:', error);
      return false;
    }
  }

  // Get queue instances for processors
  public static getPipelineQueue(): Queue {
    return QueueService.getInstance().pipelineQueue;
  }

  public static getJobQueue(): Queue {
    return QueueService.getInstance().jobQueue;
  }

  public static getNotificationQueue(): Queue {
    return QueueService.getInstance().notificationQueue;
  }
}

export { QueueService };
