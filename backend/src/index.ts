import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import dotenv from 'dotenv';

// Import services and middleware
import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { authMiddleware } from './middleware/auth';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import projectRoutes from './routes/projects';
import pipelineRoutes from './routes/pipelines';
import jobRoutes from './routes/jobs';
import secretsRoutes from './routes/secrets';
import settingsRoutes from './routes/settings';
import webhookRoutes from './routes/webhooks';
import healthRoutes from './routes/health';
import dashboardRoutes from './routes/dashboard';
import notificationRoutes from './routes/notifications';
import auditRoutes from './routes/audit';

// Import services
import { DatabaseService } from './services/database';
import { QueueService } from './services/queue';
import { SocketService } from './services/socket';
import { MinioService } from './services/minio';
import { NotificationProcessor } from './services/notificationProcessor';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || "http://localhost:3000",
  credentials: true
}));
app.use(compression() as any);
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(limiter as any);

// Health check route (before auth middleware)
app.use('/health', healthRoutes);

// Webhook routes (before auth middleware for external webhooks)
app.use('/webhooks', webhookRoutes);

// Authentication routes
app.use('/auth', authRoutes);

// Protected routes
app.use('/api/users', authMiddleware as any, userRoutes);
app.use('/api/projects', authMiddleware as any, projectRoutes);
app.use('/api/pipelines', authMiddleware as any, pipelineRoutes);
app.use('/api/jobs', authMiddleware as any, jobRoutes);
app.use('/api/secrets', authMiddleware as any, secretsRoutes);
app.use('/api/settings', authMiddleware as any, settingsRoutes);
app.use('/api/dashboard', authMiddleware as any, dashboardRoutes);
app.use('/api/notifications', authMiddleware as any, notificationRoutes);
app.use('/api/audit', authMiddleware as any, auditRoutes);

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler as any);

// Initialize services
async function initializeServices() {
  // Track initialization status of each service
  const serviceStatus = {
    database: false,
    minio: false,
    queue: false,
    socket: false,
    notification: false
  };

  try {
    // Initialize database (with fallback)
    try {
      await DatabaseService.initialize();
      serviceStatus.database = true;
      logger.info('Database service initialized successfully');
    } catch (dbError) {
      logger.warn('Database initialization failed, continuing without database:', dbError);
      // Log specific error details to help diagnose the issue
      logger.debug('Database error details:', {
        name: (dbError as Error).name,
        message: (dbError as Error).message,
        stack: (dbError as Error).stack
      });
    }

    // Initialize MinIO (with fallback)
    try {
      await MinioService.initialize();
      serviceStatus.minio = true;
      logger.info('MinIO service initialized successfully');
    } catch (minioError) {
      logger.warn('MinIO initialization failed, continuing without MinIO:', minioError);
      // Log specific error details to help diagnose the issue
      logger.debug('MinIO error details:', {
        name: (minioError as Error).name,
        message: (minioError as Error).message,
        stack: (minioError as Error).stack
      });
    }

    // Initialize queue service (with fallback)
    try {
      await QueueService.initialize();
      serviceStatus.queue = true;
      logger.info('Queue service initialized successfully');
    } catch (queueError) {
      logger.warn('Queue service initialization failed, continuing without queue:', queueError);
      // Log specific error details to help diagnose the issue
      logger.debug('Queue error details:', {
        name: (queueError as Error).name,
        message: (queueError as Error).message,
        stack: (queueError as Error).stack
      });
    }

    // Initialize socket service
    try {
      SocketService.initialize(io);
      serviceStatus.socket = true;
      logger.info('Socket service initialized successfully');
    } catch (socketError) {
      logger.warn('Socket service initialization failed:', socketError);
      // Log specific error details to help diagnose the issue
      logger.debug('Socket error details:', {
        name: (socketError as Error).name,
        message: (socketError as Error).message,
        stack: (socketError as Error).stack
      });
    }

    // Initialize notification processor only if queue service is available
    try {
      if (serviceStatus.queue) {
        NotificationProcessor.getInstance();
        serviceStatus.notification = true;
        logger.info('Notification processor initialized successfully');
      } else {
        logger.warn('Skipping notification processor initialization due to queue service failure');
      }
    } catch (notificationError) {
      logger.warn('Notification processor initialization failed:', notificationError);
      // Log specific error details to help diagnose the issue
      logger.debug('Notification error details:', {
        name: (notificationError as Error).name,
        message: (notificationError as Error).message,
        stack: (notificationError as Error).stack
      });
    }

    // Log overall initialization status
    const initializedServices = Object.entries(serviceStatus)
      .filter(([_, status]) => status)
      .map(([name, _]) => name);

    if (initializedServices.length === Object.keys(serviceStatus).length) {
      logger.info('All services initialized successfully');
    } else {
      logger.warn(`Partial initialization: ${initializedServices.length}/${Object.keys(serviceStatus).length} services started`);
      logger.info(`Successfully initialized: ${initializedServices.join(', ')}`);
      logger.warn(`Failed services: ${Object.entries(serviceStatus)
        .filter(([_, status]) => !status)
        .map(([name, _]) => name)
        .join(', ')}`);
    }
  } catch (error) {
    logger.error('Unexpected error during service initialization:', error);
    // Don't exit, continue with available services
    logger.warn('Continuing with limited functionality');
  }
}

// Graceful shutdown handler
async function gracefulShutdown(signal: string) {
  logger.info(`${signal} received, shutting down gracefully`);
  let exitCode = 0;
  
  // Track shutdown status
  const shutdownStatus = {
    httpServer: false,
    queueService: false,
    databaseService: false,
    socketService: false
  };
  
  try {
    // Close HTTP server with timeout
    const closeHttpServer = new Promise<void>((resolve) => {
      const serverCloseTimeout = setTimeout(() => {
        logger.warn('HTTP server close timed out after 5 seconds');
        resolve();
      }, 5000);
      
      server.close(() => {
        clearTimeout(serverCloseTimeout);
        logger.info('HTTP server closed successfully');
        shutdownStatus.httpServer = true;
        resolve();
      });
    });
    
    await closeHttpServer;
    
    // Close queue service with error handling
    try {
      await QueueService.close();
      logger.info('Queue service closed successfully');
      shutdownStatus.queueService = true;
    } catch (queueError) {
      logger.error('Error closing queue service:', queueError);
      exitCode = 1;
    }
    
    // Close database service with error handling
    try {
      await DatabaseService.close();
      logger.info('Database service closed successfully');
      shutdownStatus.databaseService = true;
    } catch (dbError) {
      logger.error('Error closing database service:', dbError);
      exitCode = 1;
    }
    
    // Log shutdown status
    const successfulShutdowns = Object.entries(shutdownStatus)
      .filter(([_, status]) => status)
      .map(([name, _]) => name);
    
    const failedShutdowns = Object.entries(shutdownStatus)
      .filter(([_, status]) => !status)
      .map(([name, _]) => name);
    
    if (failedShutdowns.length > 0) {
      logger.warn(`Failed to close some services: ${failedShutdowns.join(', ')}`);
      exitCode = 1;
    } else {
      logger.info('All services closed successfully');
    }
  } catch (error) {
    logger.error('Unexpected error during shutdown:', error);
    exitCode = 1;
  } finally {
    logger.info(`Exiting process with code ${exitCode}`);
    process.exit(exitCode);
  }
}

// Register signal handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start server
async function startServer() {
  try {
    await initializeServices();
    
    // Listen on all network interfaces (0.0.0.0) instead of just localhost
    server.listen(PORT, () => {
      logger.info(`🚀 ChainOps Backend Server running on port ${PORT}`);
      logger.info(`📊 Health check: http://localhost:${PORT}/health`);
      logger.info(`🔗 WebSocket server ready`);
      logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`🌐 Server is listening on all network interfaces`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Only start server if not in test environment
if (process.env.NODE_ENV !== 'test') {
  startServer();
}

export { app, server, io };
