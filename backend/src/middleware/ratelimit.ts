import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

interface RateLimitOptions {
  windowMs: number;
  max: number;
  message?: string;
  statusCode?: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

export class RateLimit {
  private store: RateLimitStore = {};
  private options: Required<RateLimitOptions>;

  private cleanupInterval: NodeJS.Timeout | null = null;
  private static instances: RateLimit[] = [];

  constructor(options: RateLimitOptions) {
    this.options = {
      windowMs: options.windowMs,
      max: options.max,
      message: options.message || 'Too many requests, please try again later.',
      statusCode: options.statusCode || 429,
      skipSuccessfulRequests: options.skipSuccessfulRequests || false,
      skipFailedRequests: options.skipFailedRequests || false,
      keyGenerator: options.keyGenerator || this.defaultKeyGenerator,
    };

    // Clean up expired entries every minute
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000);
    
    // Track instances for cleanup in tests
    RateLimit.instances.push(this);
  }
  
  /**
   * Destroys this rate limiter instance by clearing its interval
   * This should be called in test teardown to prevent memory leaks
   */
  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      
      // Remove this instance from the instances array
      const index = RateLimit.instances.indexOf(this);
      if (index !== -1) {
        RateLimit.instances.splice(index, 1);
      }
    }
  }
  
  /**
   * Destroys all rate limiter instances
   * This is useful for test teardown to prevent memory leaks
   */
  public static destroyAll(): void {
    for (const instance of [...RateLimit.instances]) {
      instance.destroy();
    }
    RateLimit.instances = [];
  }

  private defaultKeyGenerator(req: Request): string {
    return req.ip || 'unknown';
  }

  private cleanup(): void {
    const now = Date.now();
    for (const key in this.store) {
      if (this.store[key].resetTime <= now) {
        delete this.store[key];
      }
    }
  }

  middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const key = this.options.keyGenerator(req);
      const now = Date.now();
      const resetTime = now + this.options.windowMs;

      // Initialize or reset if window has passed
      if (!this.store[key] || this.store[key].resetTime <= now) {
        this.store[key] = {
          count: 0,
          resetTime,
        };
      }

      // Increment count
      this.store[key].count++;

      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', this.options.max);
      res.setHeader('X-RateLimit-Remaining', Math.max(0, this.options.max - this.store[key].count));
      res.setHeader('X-RateLimit-Reset', new Date(this.store[key].resetTime).toISOString());

      // Check if limit exceeded
      if (this.store[key].count > this.options.max) {
        logger.warn('Rate limit exceeded', {
          key,
          count: this.store[key].count,
          limit: this.options.max,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          path: req.path,
        });

        return res.status(this.options.statusCode).json({
          error: this.options.message,
          retryAfter: Math.ceil((this.store[key].resetTime - now) / 1000),
        });
      }

      return next();
    };
  }

  static create(options: RateLimitOptions) {
    return new RateLimit(options);
  }

  // Predefined rate limiters
  static strict() {
    return new RateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
    });
  }

  static moderate() {
    return new RateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 500, // limit each IP to 500 requests per windowMs
    });
  }

  static lenient() {
    return new RateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // limit each IP to 1000 requests per windowMs
    });
  }

  static auth() {
    return new RateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // limit each IP to 5 auth requests per windowMs
      message: 'Too many authentication attempts, please try again later.',
    });
  }

  static api() {
    return new RateLimit({
      windowMs: 60 * 1000, // 1 minute
      max: 60, // limit each IP to 60 API requests per minute
    });
  }
}

export const rateLimit = RateLimit;
