import { jest } from '@jest/globals';
import { RateLimit } from './src/middleware/ratelimit';
import { LogManager } from './src/observability/logs';
import { QueueService } from './src/services/queue';

// Clear all mocks after each test
afterEach(async () => {
  jest.clearAllMocks();
});

// Global teardown after all tests
afterAll(async () => {
  // Clean up RateLimit intervals
  RateLimit.destroyAll();
  
  // Clean up any open Redis connections
  try {
    await QueueService.close();
  } catch (error) {
    console.warn('Error during QueueService cleanup:', error);
  }
  
  // Allow time for async operations to complete
  await new Promise(resolve => setTimeout(resolve, 500));
});